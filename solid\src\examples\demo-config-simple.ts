/**
 * 简化版表单配置示例
 * 使用直观的验证规则，提高可读性
 */

import type { FormConfig } from "../types/form";
import { ValidationRules } from "../core/validation-simple";

// 用户注册表单示例
export const userRegistrationConfig: FormConfig = {
  title: "用户注册",
  description: "请填写以下信息完成注册",
  submitButtonText: "注册账户",
  submitUrl: "/api/register",
  layout: "vertical",
  fields: [
    {
      id: "username",
      type: "text",
      label: "用户名",
      placeholder: "请输入用户名",
      validation: {
        required: true,
        minLength: 3,
        maxLength: 20,
        pattern: /^[a-zA-Z0-9_]+$/,
      },
      description: "用户名只能包含字母、数字和下划线",
    },
    {
      id: "email",
      type: "text",
      inputType: "email",
      label: "邮箱地址",
      placeholder: "请输入邮箱地址",
      autoComplete: "email",
      validation: ValidationRules.requiredEmail(),
    },
    {
      id: "password",
      type: "text",
      inputType: "password",
      label: "密码",
      placeholder: "请输入密码",
      autoComplete: "new-password",
      validation: ValidationRules.password(),
      description: "密码至少6位，包含字母和数字",
    },
    {
      id: "gender",
      type: "radio",
      label: "性别",
      orientation: "horizontal",
      options: [
        { label: "男", value: "male" },
        { label: "女", value: "female" },
        { label: "其他", value: "other" },
      ],
      validation: ValidationRules.required(),
    },
    {
      id: "country",
      type: "select",
      label: "国家/地区",
      placeholder: "请选择国家",
      options: [
        { label: "中国", value: "cn" },
        { label: "美国", value: "us" },
        { label: "日本", value: "jp" },
        { label: "英国", value: "uk" },
        { label: "其他", value: "other" },
      ],
      validation: ValidationRules.required(),
    },
  ],
} as const;

// 联系我们表单示例
export const contactFormConfig: FormConfig = {
  title: "联系我们",
  description: "有任何问题或建议，请随时与我们联系",
  submitButtonText: "发送消息",
  submitUrl: "/api/contact",
  layout: "vertical",
  fields: [
    {
      id: "name",
      type: "text",
      label: "姓名",
      placeholder: "请输入您的姓名",
      validation: ValidationRules.required(),
    },
    {
      id: "email",
      type: "text",
      inputType: "email",
      label: "邮箱",
      placeholder: "请输入您的邮箱",
      validation: ValidationRules.requiredEmail(),
    },
    {
      id: "subject",
      type: "select",
      label: "主题",
      placeholder: "请选择消息主题",
      options: [
        { label: "一般咨询", value: "general" },
        { label: "技术支持", value: "support" },
        { label: "商务合作", value: "business" },
        { label: "意见反馈", value: "feedback" },
        { label: "其他", value: "other" },
      ],
      validation: ValidationRules.required(),
    },
  ],
} as const;

// 网格布局表单示例
export const surveyFormConfig: FormConfig = {
  title: "产品满意度调查",
  description: "您的反馈对我们很重要",
  submitButtonText: "提交调查",
  submitUrl: "/api/survey",
  layout: "grid",
  columns: 2,
  fields: [
    {
      id: "overall_rating",
      type: "radio",
      label: "总体评分",
      orientation: "horizontal",
      options: [
        { label: "1分", value: "1" },
        { label: "2分", value: "2" },
        { label: "3分", value: "3" },
        { label: "4分", value: "4" },
        { label: "5分", value: "5" },
      ],
      validation: ValidationRules.required(),
    },
    {
      id: "recommendation",
      type: "select",
      label: "推荐意愿",
      placeholder: "您是否愿意推荐给朋友？",
      options: [
        { label: "非常愿意", value: "very_likely" },
        { label: "可能会", value: "likely" },
        { label: "不确定", value: "neutral" },
        { label: "不太愿意", value: "unlikely" },
        { label: "绝不会", value: "very_unlikely" },
      ],
      validation: ValidationRules.required(),
    },
    {
      id: "features_used",
      type: "select",
      label: "使用的功能",
      placeholder: "请选择您使用过的功能",
      multiple: true,
      options: [
        { label: "基础功能", value: "basic" },
        { label: "高级功能", value: "advanced" },
        { label: "自定义功能", value: "custom" },
        { label: "集成功能", value: "integration" },
      ],
    },
    {
      id: "user_type",
      type: "radio",
      label: "用户类型",
      orientation: "vertical",
      options: [
        { label: "个人用户", value: "individual" },
        { label: "企业用户", value: "business" },
        { label: "学生", value: "student" },
        { label: "其他", value: "other" },
      ],
      validation: ValidationRules.required(),
    },
  ],
} as const;

// 高级验证示例
export const advancedValidationConfig: FormConfig = {
  title: "高级验证示例",
  description: "展示各种验证规则和自定义验证",
  submitButtonText: "提交",
  submitUrl: "/api/advanced",
  layout: "vertical",
  fields: [
    {
      id: "phone",
      type: "text",
      inputType: "tel",
      label: "手机号码",
      placeholder: "请输入手机号码",
      validation: ValidationRules.requiredPhone(),
    },
    {
      id: "website",
      type: "text",
      inputType: "url",
      label: "个人网站",
      placeholder: "https://example.com",
      validation: ValidationRules.url(),
    },
    {
      id: "age_confirm",
      type: "radio",
      label: "年龄确认",
      options: [
        { label: "我已满18岁", value: "confirmed" },
        { label: "我未满18岁", value: "not_confirmed" },
      ],
      validation: ValidationRules.ageConfirm(),
    },
    {
      id: "custom_validation",
      type: "text",
      label: "自定义验证示例",
      placeholder: '请输入包含"test"的文本',
      validation: ValidationRules.mustContain("test"),
      description: "这是一个自定义验证示例",
    },
    {
      id: "bio",
      type: "text",
      label: "个人简介",
      placeholder: "请简单介绍一下自己",
      validation: ValidationRules.length(10, 200),
      description: "10-200个字符",
    },
  ],
} as const;

// 简单表单示例
export const simpleFormConfig: FormConfig = {
  title: "简单表单",
  description: "最基础的表单示例",
  submitButtonText: "提交",
  submitUrl: "/api/simple",
  layout: "vertical",
  fields: [
    {
      id: "name",
      type: "text",
      label: "姓名",
      placeholder: "请输入姓名",
      validation: ValidationRules.required(),
    },
    {
      id: "email",
      type: "text",
      inputType: "email",
      label: "邮箱",
      placeholder: "请输入邮箱",
      validation: ValidationRules.email(),
    },
    {
      id: "message",
      type: "text",
      label: "留言",
      placeholder: "请输入留言（可选）",
    },
  ],
} as const;

// 所有配置的集合
export const demoConfigs = {
  userRegistration: userRegistrationConfig,
  contactForm: contactFormConfig,
  surveyForm: surveyFormConfig,
  advancedValidation: advancedValidationConfig,
  simpleForm: simpleFormConfig,
} as const;

export type DemoConfigKey = keyof typeof demoConfigs;
