import { useState } from "react";
import * as Form from "@radix-ui/react-form";
import * as Select from "@radix-ui/react-select";
import * as RadioGroup from "@radix-ui/react-radio-group";
import { ChevronDownIcon } from "@radix-ui/react-icons";

// 表单配置类型
interface FormOption {
  label: string;
  value: string;
}

interface TextField {
  id: string;
  type: "text";
  label: string;
  placeholder?: string;
  required?: boolean;
}

interface SelectField {
  id: string;
  type: "select";
  label: string;
  placeholder?: string;
  options: FormOption[];
  required?: boolean;
}

interface RadioField {
  id: string;
  type: "radio";
  label: string;
  options: FormOption[];
  required?: boolean;
}

type FormField = TextField | SelectField | RadioField;

export interface SimpleFormConfig {
  title: string;
  submitButtonText: string;
  submitUrl: string;
  fields: FormField[];
}

interface SimpleConfigurableFormProps {
  config: SimpleFormConfig;
}

export function SimpleConfigurableForm({
  config,
}: SimpleConfigurableFormProps) {
  const [formData, setFormData] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFieldChange = (fieldId: string, value: string) => {
    setFormData((prev) => ({ ...prev, [fieldId]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch(config.submitUrl, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        alert("表单提交成功！");
      } else {
        alert("表单提交失败，请重试。");
      }
    } catch (error) {
      alert("提交过程中发生错误。");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 py-12 px-4">
      <div className="w-full max-w-md space-y-8 rounded-lg bg-white p-8 shadow-lg">
        <h2 className="text-center text-3xl font-bold text-gray-900">
          {config.title}
        </h2>

        <Form.Root onSubmit={handleSubmit} className="space-y-6">
          {config.fields.map((field) => (
            <Form.Field key={field.id} name={field.id} className="space-y-2">
              <Form.Label className="text-base font-medium text-gray-700">
                {field.label}
                {field.required && <span className="text-red-500">*</span>}
              </Form.Label>

              {field.type === "text" && (
                <Form.Control asChild>
                  <input
                    type="text"
                    placeholder={field.placeholder}
                    value={formData[field.id] || ""}
                    onChange={(e) =>
                      handleFieldChange(field.id, e.target.value)
                    }
                    required={field.required}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </Form.Control>
              )}

              {field.type === "select" && (
                <Select.Root
                  value={formData[field.id] || ""}
                  onValueChange={(value) => handleFieldChange(field.id, value)}
                  required={field.required}
                >
                  <Select.Trigger className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-between">
                    <Select.Value placeholder={field.placeholder} />
                    <Select.Icon>
                      <ChevronDownIcon />
                    </Select.Icon>
                  </Select.Trigger>
                  <Select.Portal>
                    <Select.Content className="bg-white border border-gray-300 rounded-md shadow-lg">
                      <Select.Viewport className="p-1">
                        {field.options.map((option) => (
                          <Select.Item
                            key={option.value}
                            value={option.value}
                            className="px-3 py-2 cursor-pointer hover:bg-gray-100 rounded"
                          >
                            <Select.ItemText>{option.label}</Select.ItemText>
                          </Select.Item>
                        ))}
                      </Select.Viewport>
                    </Select.Content>
                  </Select.Portal>
                </Select.Root>
              )}

              {field.type === "radio" && (
                <RadioGroup.Root
                  value={formData[field.id] || ""}
                  onValueChange={(value) => handleFieldChange(field.id, value)}
                  required={field.required}
                  className="flex flex-col space-y-2"
                >
                  {field.options.map((option) => (
                    <div
                      key={option.value}
                      className="flex items-center space-x-2"
                    >
                      <RadioGroup.Item
                        value={option.value}
                        id={`${field.id}-${option.value}`}
                        className="w-4 h-4 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <RadioGroup.Indicator className="flex items-center justify-center w-full h-full relative after:content-[''] after:w-2 after:h-2 after:rounded-full after:bg-blue-600" />
                      </RadioGroup.Item>
                      <label
                        htmlFor={`${field.id}-${option.value}`}
                        className="text-sm"
                      >
                        {option.label}
                      </label>
                    </div>
                  ))}
                </RadioGroup.Root>
              )}

              <Form.Message
                match="valueMissing"
                className="text-sm text-red-500"
              >
                {field.label}为必填项
              </Form.Message>
            </Form.Field>
          ))}

          <Form.Submit asChild>
            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {isSubmitting ? "提交中..." : config.submitButtonText}
            </button>
          </Form.Submit>
        </Form.Root>
      </div>
    </div>
  );
}
