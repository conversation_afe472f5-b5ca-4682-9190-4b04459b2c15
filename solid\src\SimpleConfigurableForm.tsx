import { useState } from "react";
import * as Form from "@radix-ui/react-form";
import * as Select from "@radix-ui/react-select";
import * as RadioGroup from "@radix-ui/react-radio-group";
import * as Checkbox from "@radix-ui/react-checkbox";
import { ChevronDownIcon, CheckIcon } from "@radix-ui/react-icons";

// 表单选项配置
interface FormOption {
  label: string;
  value: string;
}

// 文本框配置
interface TextFieldConfig {
  id: string;
  type: "text";
  title: string;
  placeholder?: string;
  maxLength?: number;
  required?: boolean;
}

// 下拉选择器配置
interface SelectFieldConfig {
  id: string;
  type: "select";
  title: string;
  placeholder?: string;
  multiple?: boolean;
  options: FormOption[];
  required?: boolean;
}

// Radio选择器配置
interface RadioFieldConfig {
  id: string;
  type: "radio";
  title: string;
  multiple?: boolean;
  options: FormOption[];
  required?: boolean;
}

type FormFieldConfig = TextFieldConfig | SelectFieldConfig | RadioFieldConfig;

// 表单配置接口
export interface FormConfig {
  title: string;
  submitUrl: string;
  submitButtonText: string;
  fields: FormFieldConfig[];
}

interface ConfigurableFormProps {
  config: FormConfig;
}

export function ConfigurableForm({ config }: ConfigurableFormProps) {
  const [formData, setFormData] = useState<Record<string, string | string[]>>(
    {}
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleFieldChange = (fieldId: string, value: string | string[]) => {
    setFormData((prev) => ({ ...prev, [fieldId]: value }));
    // 清除该字段的错误
    if (errors[fieldId]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[fieldId];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    config.fields.forEach((field) => {
      if (field.required) {
        const value = formData[field.id];
        if (!value || (Array.isArray(value) && value.length === 0)) {
          newErrors[field.id] = `${field.title}为必填项`;
        }
      }

      // 文本框字符数验证
      if (field.type === "text" && field.maxLength && formData[field.id]) {
        const textValue = formData[field.id] as string;
        if (textValue.length > field.maxLength) {
          newErrors[
            field.id
          ] = `${field.title}不能超过${field.maxLength}个字符`;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch(config.submitUrl, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        alert("表单提交成功！");
        setFormData({});
      } else {
        alert("表单提交失败，请重试。");
      }
    } catch (error) {
      alert("提交过程中发生错误。");
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderTextField = (field: TextFieldConfig) => (
    <Form.Control asChild>
      <input
        type="text"
        placeholder={field.placeholder}
        value={(formData[field.id] as string) || ""}
        onChange={(e) => handleFieldChange(field.id, e.target.value)}
        maxLength={field.maxLength}
        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </Form.Control>
  );

  const renderSelectField = (field: SelectFieldConfig) => {
    if (field.multiple) {
      const selectedValues = (formData[field.id] as string[]) || [];

      return (
        <div className="space-y-2">
          {field.options.map((option) => (
            <div key={option.value} className="flex items-center space-x-2">
              <Checkbox.Root
                checked={selectedValues.includes(option.value)}
                onCheckedChange={(checked) => {
                  const newValues = checked
                    ? [...selectedValues, option.value]
                    : selectedValues.filter((v) => v !== option.value);
                  handleFieldChange(field.id, newValues);
                }}
                className="w-4 h-4 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <Checkbox.Indicator>
                  <CheckIcon className="w-3 h-3" />
                </Checkbox.Indicator>
              </Checkbox.Root>
              <label className="text-sm">{option.label}</label>
            </div>
          ))}
        </div>
      );
    }

    return (
      <Select.Root
        value={(formData[field.id] as string) || ""}
        onValueChange={(value) => handleFieldChange(field.id, value)}
      >
        <Select.Trigger className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-between">
          <Select.Value placeholder={field.placeholder} />
          <Select.Icon>
            <ChevronDownIcon />
          </Select.Icon>
        </Select.Trigger>
        <Select.Portal>
          <Select.Content className="bg-white border border-gray-300 rounded-md shadow-lg">
            <Select.Viewport className="p-1">
              {field.options.map((option) => (
                <Select.Item
                  key={option.value}
                  value={option.value}
                  className="px-3 py-2 cursor-pointer hover:bg-gray-100 rounded"
                >
                  <Select.ItemText>{option.label}</Select.ItemText>
                </Select.Item>
              ))}
            </Select.Viewport>
          </Select.Content>
        </Select.Portal>
      </Select.Root>
    );
  };

  const renderRadioField = (field: RadioFieldConfig) => {
    if (field.multiple) {
      const selectedValues = (formData[field.id] as string[]) || [];

      return (
        <div className="space-y-2">
          {field.options.map((option) => (
            <div key={option.value} className="flex items-center space-x-2">
              <Checkbox.Root
                checked={selectedValues.includes(option.value)}
                onCheckedChange={(checked) => {
                  const newValues = checked
                    ? [...selectedValues, option.value]
                    : selectedValues.filter((v) => v !== option.value);
                  handleFieldChange(field.id, newValues);
                }}
                className="w-4 h-4 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <Checkbox.Indicator>
                  <CheckIcon className="w-3 h-3" />
                </Checkbox.Indicator>
              </Checkbox.Root>
              <label className="text-sm">{option.label}</label>
            </div>
          ))}
        </div>
      );
    }

    return (
      <RadioGroup.Root
        value={(formData[field.id] as string) || ""}
        onValueChange={(value) => handleFieldChange(field.id, value)}
        className="flex flex-col space-y-2"
      >
        {field.options.map((option) => (
          <div key={option.value} className="flex items-center space-x-2">
            <RadioGroup.Item
              value={option.value}
              id={`${field.id}-${option.value}`}
              className="w-4 h-4 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <RadioGroup.Indicator className="flex items-center justify-center w-full h-full relative after:content-[''] after:w-2 after:h-2 after:rounded-full after:bg-blue-600" />
            </RadioGroup.Item>
            <label htmlFor={`${field.id}-${option.value}`} className="text-sm">
              {option.label}
            </label>
          </div>
        ))}
      </RadioGroup.Root>
    );
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 py-12 px-4">
      <div className="w-full max-w-md space-y-8 rounded-lg bg-white p-8 shadow-lg">
        <h2 className="text-center text-3xl font-bold text-gray-900">
          {config.title}
        </h2>

        <Form.Root onSubmit={handleSubmit} className="space-y-6">
          {config.fields.map((field) => (
            <Form.Field key={field.id} name={field.id} className="space-y-2">
              <Form.Label className="text-base font-medium text-gray-700">
                {field.title}
                {field.required && <span className="text-red-500">*</span>}
                {field.type === "text" && field.maxLength && (
                  <span className="text-sm text-gray-500 ml-2">
                    ({((formData[field.id] as string) || "").length}/
                    {field.maxLength})
                  </span>
                )}
              </Form.Label>

              {field.type === "text" && renderTextField(field)}
              {field.type === "select" && renderSelectField(field)}
              {field.type === "radio" && renderRadioField(field)}

              {errors[field.id] && (
                <div className="text-sm text-red-500">{errors[field.id]}</div>
              )}
            </Form.Field>
          ))}

          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {isSubmitting ? "提交中..." : config.submitButtonText}
          </button>
        </Form.Root>
      </div>
    </div>
  );
}
