/**
 * 表单系统类型定义
 */

// 基础接口和联合类型
export interface FormOption {
  readonly label: string;
  readonly value: string;
  readonly disabled?: boolean;
}

// 字段验证规则接口
export interface ValidationRule {
  readonly required?: boolean;
  readonly minLength?: number;
  readonly maxLength?: number;
  readonly pattern?: RegExp;
  readonly custom?: (value: unknown) => string | null;
}

// 字段基础接口
export interface BaseFormField {
  readonly id: string;
  readonly label: string;
  readonly placeholder?: string;
  readonly defaultValue?: string;
  readonly validation?: ValidationRule;
  readonly disabled?: boolean;
  readonly description?: string;
}

// 具体字段类型接口
export interface TextFieldConfig extends BaseFormField {
  readonly type: "text";
  readonly inputType?: "text" | "email" | "password" | "tel" | "url";
  readonly autoComplete?: string;
}

export interface TextareaFieldConfig extends BaseFormField {
  readonly type: "textarea";
  readonly rows?: number;
  readonly resize?: boolean;
}

export interface SelectFieldConfig extends BaseFormField {
  readonly type: "select";
  readonly options: readonly FormOption[];
  readonly multiple?: boolean;
  readonly searchable?: boolean;
}

export interface RadioFieldConfig extends BaseFormField {
  readonly type: "radio";
  readonly options: readonly FormOption[];
  readonly orientation?: "horizontal" | "vertical";
}

export interface CheckboxFieldConfig extends BaseFormField {
  readonly type: "checkbox";
  readonly options?: readonly FormOption[]; // 多选框组
}

export interface SwitchFieldConfig extends BaseFormField {
  readonly type: "switch";
}

export interface DateFieldConfig extends BaseFormField {
  readonly type: "date";
  readonly minDate?: Date;
  readonly maxDate?: Date;
  readonly format?: string;
}

export interface NumberFieldConfig extends BaseFormField {
  readonly type: "number";
  readonly min?: number;
  readonly max?: number;
  readonly step?: number;
}

// 联合类型
export type FormFieldConfig =
  | TextFieldConfig
  | TextareaFieldConfig
  | SelectFieldConfig
  | RadioFieldConfig
  | CheckboxFieldConfig
  | SwitchFieldConfig
  | DateFieldConfig
  | NumberFieldConfig;

// 表单配置接口
export interface FormConfig {
  readonly title: string;
  readonly description?: string;
  readonly submitButtonText: string;
  readonly submitUrl: string;
  readonly fields: readonly FormFieldConfig[];
  readonly layout?: "vertical" | "horizontal" | "grid";
  readonly columns?: number;
}

// 表单提交数据类型
export type FormData = Record<string, unknown>;

// 验证错误类型
export interface ValidationError {
  readonly field: string;
  readonly message: string;
  readonly code?: string;
}

// 表单状态接口
export interface FormState {
  readonly isSubmitting: boolean;
  readonly isValid: boolean;
  readonly errors: readonly ValidationError[];
  readonly touched: Record<string, boolean>;
  readonly data: FormData;
}

// 字段渲染器接口
export interface FieldRenderer<T extends FormFieldConfig = FormFieldConfig> {
  render(
    field: T,
    value: unknown,
    onChange: (value: unknown) => void,
    error?: string,
    touched?: boolean
  ): React.ReactElement;
}

// 表单事件处理接口
export interface FormEventHandlers {
  onSubmit: (data: FormData) => Promise<void> | void;
  onChange?: (field: string, value: unknown) => void;
  onValidation?: (errors: readonly ValidationError[]) => void;
}

// 表单上下文接口
export interface FormContextValue {
  readonly state: FormState;
  readonly updateField: (field: string, value: unknown) => void;
  readonly validateField: (field: string) => Promise<string | null>;
  readonly submitForm: () => Promise<void>;
  readonly resetForm: () => void;
}

// 错误边界类型
export interface FormError extends Error {
  readonly field?: string;
  readonly code?: string;
  readonly recoverable?: boolean;
}
